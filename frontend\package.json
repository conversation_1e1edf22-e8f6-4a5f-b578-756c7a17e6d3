{"name": "frontend", "version": "1.0.0", "type": "module", "directories": {"doc": "docs", "test": "tests"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint src/ --ext .js,.vue", "lint:fix": "eslint src/ --ext .js,.vue --fix", "format": "prettier --write src/"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.11.0", "element-plus": "^2.10.4", "hotkeys-js": "^3.13.15", "pinia": "^2.3.1", "pinia-plugin-persistedstate": "^3.2.3", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.6.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/test-utils": "^2.4.6", "eslint": "^8.57.1", "jsdom": "^22.1.0", "prettier": "^3.6.2", "sass": "^1.89.2", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.25.2", "vite": "^4.5.14", "vitest": "^0.34.6"}}