import type { TSchema, SchemaOptions } from '../schema/index';
import type { Ensure, Evaluate } from '../helpers/index';
import type { TProperties } from '../object/index';
import { type TIndex } from './indexed';
import { type TMappedResult, type TMappedKey } from '../mapped/index';
type TMappedIndexPropertyKey<Type extends TSchema, Key extends PropertyKey> = {
    [_ in Key]: TIndex<Type, [Key]>;
};
type TMappedIndexPropertyKeys<Type extends TSchema, PropertyKeys extends PropertyKey[], Result extends TProperties = {}> = (PropertyKeys extends [infer Left extends PropertyKey, ...infer Right extends PropertyKey[]] ? TMappedIndexPropertyKeys<Type, Right, Result & TMappedIndexPropertyKey<Type, Left>> : Result);
type TMappedIndexProperties<Type extends TSchema, <PERSON>ped<PERSON><PERSON> extends TMappedKey> = Evaluate<TMappedIndexPropertyKeys<Type, MappedKey['keys']>>;
export type TIndexFromMappedKey<Type extends TSchema, Mapped<PERSON><PERSON> extends TMappedKey, Properties extends TProperties = TMappedIndexProperties<Type, MappedKey>> = (Ensure<TMappedResult<Properties>>);
export declare function IndexFromMappedKey<Type extends TSchema, MappedKey extends TMappedKey, Properties extends TProperties = TMappedIndexProperties<Type, MappedKey>>(type: Type, mappedKey: MappedKey, options?: SchemaOptions): TMappedResult<Properties>;
export {};
