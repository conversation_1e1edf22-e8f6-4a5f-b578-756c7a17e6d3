import sqlite3
import os

# 数据库文件路径
DB_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), '../notes.db'))
# SQL 脚本路径
SQL_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), 'create_table.sql'))

def run_sql_script(db_path, sql_path):
    with open(sql_path, 'r', encoding='utf-8') as f:
        sql = f.read()
    conn = sqlite3.connect(db_path)
    try:
        conn.executescript(sql)
        print(f"数据库已创建，表结构和索引已初始化: {db_path}")
    finally:
        conn.close()

if __name__ == '__main__':
    run_sql_script(DB_PATH, SQL_PATH)
