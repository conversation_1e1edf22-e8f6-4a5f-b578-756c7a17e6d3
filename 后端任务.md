# 后端任务清单

以下为“后端框架.md”中每个脚本的编写任务，包含功能描述、主要函数/变量、依赖关系（以task id表示），测试相关任务放在最后。

---

## 1. 编写 Express 应用主入口（src/app.js）
- 功能：搭建 Express 应用，配置中间件、路由、错误处理，初始化数据库，启动 HTTP 服务器。
- 主要函数/变量：app, server
- 依赖：2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16

## 2. 编写数据库配置与初始化（src/config/database.js）
- 功能：配置 SQLite 数据库连接，创建表结构，提供基础操作方法。
- 主要函数/变量：db, initDatabase(), closeDatabase()
- 依赖：17

## 3. 编写目录数据模型（src/models/Category.js）
- 功能：封装目录相关数据库操作，提供 CRUD、排序方法。
- 主要函数/变量：Category, getAllCategories(), getCategoryById(id), createCategory(data), updateCategory(id, data), deleteCategory(id), updateCategoryOrder(id, sortOrder)
- 依赖：2, 17

## 4. 编写笔记数据模型（src/models/Note.js）
- 功能：封装笔记相关数据库操作，提供 CRUD、排序、重要性标记方法。
- 主要函数/变量：Note, getAllNotes(), getNotesByCategory(categoryId), getNoteById(id), createNote(data), updateNote(id, data), deleteNote(id), updateNoteOrder(id, sortOrder), toggleNoteImportance(id)
- 依赖：2, 17, 3

## 5. 编写目录服务层（src/services/CategoryService.js）
- 功能：处理目录业务逻辑，验证规则，复杂操作。
- 主要函数/变量：CategoryService, getAllCategoriesWithCount(), createCategoryWithValidation(data), updateCategoryWithValidation(id, data), deleteCategoryWithCheck(id), reorderCategories(orders)
- 依赖：3, 4, 18, 17

## 6. 编写笔记服务层（src/services/NoteService.js）
- 功能：处理笔记业务逻辑，搜索、筛选、排序等。
- 主要函数/变量：NoteService, getNotesByCategoryWithPagination(categoryId, page, limit), createNoteWithValidation(data), updateNoteWithValidation(id, data), deleteNoteWithCheck(id), reorderNotes(categoryId, orders), searchNotes(keyword, categoryId), getImportantNotes()
- 依赖：4, 3, 18, 17

## 7. 编写目录控制器（src/controllers/CategoryController.js）
- 功能：处理目录相关 HTTP 请求，参数验证，响应格式化。
- 主要函数/变量：CategoryController, getAllCategories, getCategoryById, createCategory, updateCategory, deleteCategory, reorderCategories
- 依赖：5, 19, 17

## 8. 编写笔记控制器（src/controllers/NoteController.js）
- 功能：处理笔记相关 HTTP 请求，参数验证，响应格式化。
- 主要函数/变量：NoteController, getNotesByCategory, getNoteById, createNote, updateNote, deleteNote, reorderNotes, toggleImportance, searchNotes
- 依赖：6, 19, 17, 7

## 9. 编写主路由配置（src/routes/index.js）
- 功能：整合所有模块路由，设置前缀和中间件。
- 主要函数/变量：router
- 依赖：10, 11, 12

## 10. 编写目录路由（src/routes/categoryRoutes.js）
- 功能：定义目录相关 API 路由，应用中间件。
- 主要函数/变量：categoryRouter
- 依赖：7, 13

## 11. 编写笔记路由（src/routes/noteRoutes.js）
- 功能：定义笔记相关 API 路由，应用中间件。
- 主要函数/变量：noteRouter
- 依赖：8, 13

## 12. 编写请求日志中间件（src/middleware/requestLogger.js）
- 功能：记录 HTTP 请求信息和响应时间。
- 主要函数/变量：requestLogger
- 依赖：17

## 13. 编写请求数据验证中间件（src/middleware/validation.js）
- 功能：请求参数和体验证，返回统一错误响应。
- 主要函数/变量：validateCategoryInput, validateNoteInput, validateId
- 依赖：19

## 14. 编写 CORS 配置中间件（src/middleware/corsOptions.js）
- 功能：设置允许的域名、方法和头部，处理预检请求。
- 主要函数/变量：corsOptions
- 依赖：无

## 15. 编写全局错误处理中间件（src/middleware/errorHandler.js）
- 功能：统一错误响应格式，记录错误日志。
- 主要函数/变量：errorHandler
- 依赖：17

## 16. 编写应用配置管理（src/config/config.js）
- 功能：环境变量读取和默认值设置，环境切换。
- 主要函数/变量：config, PORT, DB_PATH, LOG_LEVEL, CORS_ORIGIN
- 依赖：无

## 17. 编写日志工具（src/utils/logger.js）
- 功能：日志记录，支持多级别输出和文件/控制台。
- 主要函数/变量：logger, logInfo, logError, logWarn
- 依赖：无

## 18. 编写数据验证工具（src/utils/validator.js）
- 功能：数据验证、格式化和清理。
- 主要函数/变量：validateCategoryData, validateNoteData, sanitizeInput, validateId
- 依赖：无

## 19. 编写统一响应处理工具（src/utils/responseHandler.js）
- 功能：标准化 HTTP 成功/错误响应格式。
- 主要函数/变量：sendSuccess, sendError, sendCreated, sendNoContent
- 依赖：无

---

# 测试相关任务

## 20. 编写测试环境配置（tests/setup.js）
- 功能：Jest 全局配置，测试数据库初始化和清理。
- 主要函数/变量：setupTestDatabase(), cleanupTestDatabase()
- 依赖：2, 1

## 21. 编写目录控制器测试（tests/controllers/categoryController.test.js）
- 功能：目录 API 测试，覆盖正常和异常情况。
- 主要函数/变量：createTestCategory, deleteTestCategory
- 依赖：20, 1

## 22. 编写笔记控制器测试（tests/controllers/noteController.test.js）
- 功能：笔记 API 测试，数据验证和业务逻辑测试。
- 主要函数/变量：createTestNote, deleteTestNote
- 依赖：20, 1, 21

## 23. 编写目录模型单元测试（tests/models/Category.test.js）
- 功能：目录模型数据库操作和约束测试。
- 依赖：20, 3

## 24. 编写笔记模型单元测试（tests/models/Note.test.js）
- 功能：笔记模型数据库操作和外键关系测试。
- 依赖：20, 4, 3

## 25. 编写目录服务层测试（tests/services/CategoryService.test.js）
- 功能：目录服务业务逻辑和规则测试。
- 依赖：20, 5

## 26. 编写笔记服务层测试（tests/services/NoteService.test.js）
- 功能：笔记服务高级功能和数据关系测试。
- 依赖：20, 6

## 27. 编写 API 集成测试（tests/integration/api.test.js）
- 功能：完整 API 流程和模块交互测试。
- 依赖：20, 1

## 28. 编写验证工具函数测试（tests/utils/validator.test.js）
- 功能：验证工具函数的单元测试。
- 依赖：18

