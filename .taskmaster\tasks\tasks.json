{"master": {"tasks": [{"id": 1, "title": "编写 Express 应用主入口（src/app.js）", "description": "搭建 Express 应用，配置中间件、路由、错误处理，初始化数据库，启动 HTTP 服务器。实现 app, server 变量。依赖 src/routes/index.js 的 router，src/middleware/errorHandler.js 的 errorHandler，src/middleware/corsOptions.js 的 corsOptions，src/config/database.js 的 initDatabase，src/utils/logger.js 的 logger。", "dependsOn": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], "status": "pending"}, {"id": 2, "title": "编写数据库配置与初始化（src/config/database.js）", "description": "配置 SQLite 数据库连接，创建表结构，提供 db, initDatabase(), closeDatabase()。依赖 src/utils/logger.js 的 logger。", "dependsOn": [17], "status": "pending"}, {"id": 3, "title": "编写目录数据模型（src/models/Category.js）", "description": "封装目录相关数据库操作，提供 Category 类及 getAllCategories, getCategoryById, createCategory, updateCategory, deleteCategory, updateCategoryOrder。依赖 src/config/database.js 的 db，src/utils/logger.js 的 logger。", "dependsOn": [2, 17], "status": "pending"}, {"id": 4, "title": "编写笔记数据模型（src/models/Note.js）", "description": "封装笔记相关数据库操作，提供 Note 类及 getAllNotes, getNotesByCategory, getNoteById, createNote, updateNote, deleteNote, updateNoteOrder, toggleNoteImportance。依赖 src/config/database.js 的 db，src/utils/logger.js 的 logger，src/models/Category.js。", "dependsOn": [2, 17, 3], "status": "pending"}, {"id": 5, "title": "编写目录服务层（src/services/CategoryService.js）", "description": "处理目录业务逻辑，验证规则，复杂操作。实现 CategoryService 类及 getAllCategoriesWithCount, createCategoryWithValidation, updateCategoryWithValidation, deleteCategoryWithCheck, reorderCategories。依赖 src/models/Category.js, src/models/Note.js, src/utils/validator.js, src/utils/logger.js。", "dependsOn": [3, 4, 18, 17], "status": "pending"}, {"id": 6, "title": "编写笔记服务层（src/services/NoteService.js）", "description": "处理笔记业务逻辑，搜索、筛选、排序等。实现 NoteService 类及 getNotesByCategoryWithPagination, createNoteWithValidation, updateNoteWithValidation, deleteNoteWithCheck, reorderNotes, searchNotes, getImportantNotes。依赖 src/models/Note.js, src/models/Category.js, src/utils/validator.js, src/utils/logger.js。", "dependsOn": [4, 3, 18, 17], "status": "pending"}, {"id": 7, "title": "编写目录控制器（src/controllers/CategoryController.js）", "description": "处理目录相关 HTTP 请求，参数验证，响应格式化。实现 CategoryController 类及 getAllCategories, getCategoryById, createCategory, updateCategory, deleteCategory, reorderCategories。依赖 src/services/CategoryService.js, src/utils/responseHandler.js, src/utils/logger.js。", "dependsOn": [5, 19, 17], "status": "pending"}, {"id": 8, "title": "编写笔记控制器（src/controllers/NoteController.js）", "description": "处理笔记相关 HTTP 请求，参数验证，响应格式化。实现 NoteController 类及 getNotesByCategory, getNoteById, createNote, updateNote, deleteNote, reorderNotes, toggleImportance, searchNotes。依赖 src/services/NoteService.js, src/utils/responseHandler.js, src/utils/logger.js, src/controllers/CategoryController.js。", "dependsOn": [6, 19, 17, 7], "status": "pending"}, {"id": 9, "title": "编写主路由配置（src/routes/index.js）", "description": "整合所有模块路由，设置前缀和中间件。实现 router 变量。依赖 src/routes/categoryRoutes.js, src/routes/noteRoutes.js, src/middleware/requestLogger.js。", "dependsOn": [10, 11, 12], "status": "pending"}, {"id": 10, "title": "编写目录路由（src/routes/categoryRoutes.js）", "description": "定义目录相关 API 路由，应用相关中间件。实现 categoryRouter 变量。依赖 src/controllers/CategoryController.js, src/middleware/validation.js。", "dependsOn": [7, 13], "status": "pending"}, {"id": 11, "title": "编写笔记路由（src/routes/noteRoutes.js）", "description": "定义笔记相关 API 路由，应用相关中间件。实现 noteRouter 变量。依赖 src/controllers/NoteController.js, src/middleware/validation.js。", "dependsOn": [8, 13], "status": "pending"}, {"id": 12, "title": "编写请求日志中间件（src/middleware/requestLogger.js）", "description": "记录 HTTP 请求信息和响应时间。实现 requestLogger 变量。依赖 src/utils/logger.js。", "dependsOn": [17], "status": "pending"}, {"id": 13, "title": "编写请求数据验证中间件（src/middleware/validation.js）", "description": "请求参数和体验证，返回统一错误响应。实现 validateCategoryInput, validateNoteInput, validateId。依赖 src/utils/responseHandler.js。", "dependsOn": [19], "status": "pending"}, {"id": 14, "title": "编写 CORS 配置中间件（src/middleware/corsOptions.js）", "description": "设置允许的域名、方法和头部，处理预检请求。实现 corsOptions 变量。", "dependsOn": [], "status": "pending"}, {"id": 15, "title": "编写全局错误处理中间件（src/middleware/errorHandler.js）", "description": "统一错误响应格式，记录错误日志。实现 errorHandler 变量。依赖 src/utils/logger.js。", "dependsOn": [17], "status": "pending"}, {"id": 16, "title": "编写应用配置管理（src/config/config.js）", "description": "环境变量读取和默认值设置，环境切换。实现 config, PORT, DB_PATH, LOG_LEVEL, CORS_ORIGIN。", "dependsOn": [], "status": "pending"}, {"id": 17, "title": "编写日志工具（src/utils/logger.js）", "description": "日志记录，支持多级别输出和文件/控制台。实现 logger, logInfo, logError, logWarn。", "dependsOn": [], "status": "pending"}, {"id": 18, "title": "编写数据验证工具（src/utils/validator.js）", "description": "数据验证、格式化和清理。实现 validateCategoryData, validateNoteData, sanitizeInput, validateId。", "dependsOn": [], "status": "pending"}, {"id": 19, "title": "编写统一响应处理工具（src/utils/responseHandler.js）", "description": "标准化 HTTP 成功/错误响应格式。实现 sendSuccess, sendError, sendCreated, sendNoContent。", "dependsOn": [], "status": "pending"}, {"id": 20, "title": "编写测试环境配置（tests/setup.js）", "description": "Jest 全局配置，测试数据库初始化和清理。实现 setupTestDatabase, cleanupTestDatabase。依赖 src/config/database.js, src/app.js。", "dependsOn": [2, 1], "status": "pending"}, {"id": 21, "title": "编写目录控制器测试（tests/controllers/categoryController.test.js）", "description": "目录 API 测试，覆盖正常和异常情况。实现 createTestCategory, deleteTestCategory。依赖 tests/setup.js, src/app.js。", "dependsOn": [20, 1], "status": "pending"}, {"id": 22, "title": "编写笔记控制器测试（tests/controllers/noteController.test.js）", "description": "笔记 API 测试，数据验证和业务逻辑测试。实现 createTestNote, deleteTestNote。依赖 tests/setup.js, src/app.js, tests/controllers/categoryController.test.js。", "dependsOn": [20, 1, 21], "status": "pending"}, {"id": 23, "title": "编写目录模型单元测试（tests/models/Category.test.js）", "description": "目录模型数据库操作和约束测试。依赖 tests/setup.js, src/models/Category.js。", "dependsOn": [20, 3], "status": "pending"}, {"id": 24, "title": "编写笔记模型单元测试（tests/models/Note.test.js）", "description": "笔记模型数据库操作和外键关系测试。依赖 tests/setup.js, src/models/Note.js, src/models/Category.js。", "dependsOn": [20, 4, 3], "status": "pending"}, {"id": 25, "title": "编写目录服务层测试（tests/services/CategoryService.test.js）", "description": "目录服务业务逻辑和规则测试。依赖 tests/setup.js, src/services/CategoryService.js。", "dependsOn": [20, 5], "status": "pending"}, {"id": 26, "title": "编写笔记服务层测试（tests/services/NoteService.test.js）", "description": "笔记服务高级功能和数据关系测试。依赖 tests/setup.js, src/services/NoteService.js。", "dependsOn": [20, 6], "status": "pending"}, {"id": 27, "title": "编写 API 集成测试（tests/integration/api.test.js）", "description": "完整 API 流程和模块交互测试。依赖 tests/setup.js, src/app.js。", "dependsOn": [20, 1], "status": "pending"}, {"id": 28, "title": "编写验证工具函数测试（tests/utils/validator.test.js）", "description": "验证工具函数的单元测试。依赖 src/utils/validator.js。", "dependsOn": [18], "status": "pending"}], "metadata": {"created": "2025-07-14T12:26:34.657Z", "updated": "2025-07-27T00:00:00.000Z", "description": "Tasks for master context, imported from 后端任务.md and 后端框架.md."}}}